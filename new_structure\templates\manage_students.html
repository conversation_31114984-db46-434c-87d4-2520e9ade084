<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Students - Hillview School (Class Teacher)</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/manage_pages_enhanced.css') }}">
    <style>
        /* Override container styles for manage pages */
        .manage-container {
            max-width: 95% !important;
            width: 95% !important;
            margin: 80px auto 60px !important;
            padding: var(--spacing-xl) !important;
        }

        /* Forms grid layout */
        .forms-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-xl);
            margin: var(--spacing-xl) 0;
        }

        .form-card {
            background: var(--white);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }

        .form-card h2 {
            color: var(--primary-color);
            margin-bottom: var(--spacing-lg);
            font-size: 1.3rem;
        }

        .level-selector {
            background: var(--white);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .students-section {
            background: var(--white);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-top: var(--spacing-xl);
        }

        .students-section h2 {
            color: var(--primary-color);
            margin-bottom: var(--spacing-lg);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .manage-container {
                max-width: 98% !important;
                width: 98% !important;
                padding: var(--spacing-md) !important;
            }

            .forms-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-lg);
            }
        }

        .checkbox-column {
            width: 40px;
            text-align: center;
        }
        .delete-all-btn, .delete-selected-btn, .update-btn {
            background-color: #dc3545;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .delete-all-btn:hover, .delete-selected-btn:hover {
            background-color: #c82333;
        }
        .update-btn {
            background-color: #28a745;  /* Green for update button */
        }
        .update-btn:hover {
            background-color: #218838;
        }
        .message {
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 4px;
            transition: opacity 0.5s ease;
        }
        .message-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .message-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        .message-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .teacher-info {
            font-style: italic;
            color: #666;
            margin-bottom: 15px;
            font-size: 0.9em;
        }

        /* Pagination Styles */
        .pagination-info {
            margin: 10px 0;
            font-size: 0.9em;
            color: #666;
        }

        .pagination-controls {
            margin: 20px 0;
            text-align: center;
        }

        .pagination {
            display: inline-block;
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .pagination li {
            display: inline-block;
            margin: 0 2px;
        }

        .pagination li a, .pagination li span {
            display: block;
            padding: 8px 12px;
            text-decoration: none;
            color: #333;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .pagination li a:hover {
            background-color: #e9e9e9;
        }

        .pagination li.active span {
            background-color: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }

        .pagination li.disabled span {
            color: #999;
            cursor: not-allowed;
        }

        /* Filter Form Styles */
        .filter-form {
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .filter-form h3 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.2em;
            color: #333;
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .filter-buttons {
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }

        .filter-btn {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .filter-btn:hover {
            background-color: #45a049;
        }

        .reset-btn {
            padding: 8px 15px;
            background-color: #f44336;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            display: inline-block;
        }

        .reset-btn:hover {
            background-color: #d32f2f;
        }

        /* Download Buttons Styles */
        .download-buttons {
            margin: 20px 0;
            padding: 15px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .download-buttons h3 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.2em;
            color: #333;
        }

        .download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .download-btn {
            padding: 8px 15px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-align: center;
            display: inline-block;
        }

        .download-btn:hover {
            background-color: #0b7dda;
        }

        .download-select select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
        }

        /* Search Box Styles */
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        input[type="text"]:focus {
            border-color: #4CAF50;
            outline: none;
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
        }

        /* Bulk Actions Styles */
        .bulk-actions-section {
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .bulk-actions-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.2em;
            color: #333;
        }

        .bulk-actions-section h4 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 1em;
            color: #555;
        }

        .bulk-actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .bulk-action-group {
            padding: 15px;
            background-color: #fff;
            border: 1px solid #eee;
            border-radius: 4px;
        }

        .bulk-edit-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .edit-option-group {
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
            margin-top: 10px;
        }

        .delete-all-btn, .delete-selected-btn {
            padding: 8px 15px;
            margin-right: 10px;
            margin-bottom: 10px;
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .delete-all-btn:hover, .delete-selected-btn:hover {
            background-color: #d32f2f;
        }

        .update-btn {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .update-btn:hover {
            background-color: #45a049;
        }

        /* Table improvements */
        .table-responsive {
            overflow-x: auto;
            margin: var(--spacing-lg) 0;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            background: var(--white);
        }

        table {
            width: 100%;
            min-width: 800px; /* Ensure table doesn't get too cramped */
            border-collapse: collapse;
        }

        th, td {
            padding: var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            white-space: nowrap;
        }

        th {
            background: var(--primary-color);
            color: var(--white);
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        tr:nth-child(even) {
            background: rgba(31, 125, 83, 0.05);
        }

        tr:hover {
            background: rgba(31, 125, 83, 0.1);
        }

        /* Improve button styling */
        .manage-btn, .filter-btn, .download-btn {
            background: var(--primary-color);
            color: var(--white);
            border: none;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .manage-btn:hover, .filter-btn:hover, .download-btn:hover {
            background: var(--secondary-color);
            text-decoration: none;
        }

        /* Navigation links styling */
        .manage-container > a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            margin-right: var(--spacing-md);
        }

        .manage-container > a:hover {
            text-decoration: underline;
        }

        /* Page title styling */
        .manage-container h1 {
            color: var(--primary-color);
            margin-bottom: var(--spacing-lg);
            font-size: 2.5rem;
        }
    </style>
</head>
<body>
    <div class="container manage-container">
        <header class="page-header">
            <h1>👨‍🎓 Manage Students</h1>
            <p class="page-subtitle">
                Add, edit, and manage student profiles and information
            </p>
            <div class="nav-links">
                <a href="{{ url_for('classteacher.teacher_management_hub') }}">Teacher Hub</a>
                <a href="{{ url_for('classteacher.dashboard') }}">Dashboard</a>
                <a href="{{ url_for('auth.logout_route') }}">Logout</a>
            </div>
        </header>

        <!-- Dynamic Message Container -->
        <div id="message-container"></div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="message message-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% if error_message %}
            <p class="error">{{ error_message }}</p>
        {% endif %}
        {% if success_message %}
            <p class="success">{{ success_message }}</p>
        {% endif %}

        <!-- Select Educational Level -->
        <div class="level-selector">
            <form id="educational-level-form">
                <label for="educational_level">Educational Level:</label>
                <select name="educational_level" id="educational_level" onchange="updateAllForms()">
                    <option value="">Select Educational Level</option>
                    {% for level in educational_levels %}
                        <option value="{{ level }}" {% if selected_level == level %}selected{% endif %}>{{ level }}</option>
                    {% endfor %}
                </select>
            </form>
        </div>

        <!-- Two column layout for forms -->
        <div class="forms-grid">

            <!-- Form to Add a Student -->
            <div class="form-card">
                <h2>➕ Add New Student</h2>
                <form method="POST" action="{{ url_for('classteacher.manage_students') }}" id="add-student-form">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <input type="hidden" name="action" value="add_student">
                    <input type="hidden" name="educational_level" id="educational_level_student">
                    <div class="form-group">
                        <label for="name">Student Name:</label>
                        <input type="text" name="name" id="name" required>
                    </div>
                    <div class="form-group">
                        <label for="admission_number">Admission Number:</label>
                        <input type="text" name="admission_number" id="admission_number" required>
                    </div>
                    <div class="form-group">
                        <label for="grade">Grade:</label>
                        <select name="grade" id="grade" required onchange="updateStreams()">
                            <option value="">Select Grade</option>
                            <!-- Will be populated by JavaScript -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="stream">Stream (Optional):</label>
                        <select name="stream" id="stream">
                            <option value="">-- No Stream (Optional) --</option>
                            <!-- Will be populated by JavaScript -->
                        </select>
                    </div>
                    <!-- Add Gender Field -->
                    <div class="form-group">
                        <label for="gender">Gender:</label>
                        <select name="gender" id="gender" required>
                            <option value="">Select Gender</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                        </select>
                    </div>
                    <button type="submit" class="manage-btn">Add Student</button>
                </form>
            </div>

            <!-- Form to Bulk Upload Students -->
            <div class="form-card">
                <h2>Bulk Upload Students</h2>
                <form method="POST" action="{{ url_for('classteacher.manage_students') }}" enctype="multipart/form-data" id="bulk-upload-form">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <input type="hidden" name="action" value="bulk_upload_students">
                    <input type="hidden" name="educational_level" id="educational_level_bulk">
                    <div class="form-group">
                        <label for="grade_bulk">Grade:</label>
                        <select name="grade_bulk" id="grade_bulk" required onchange="updateBulkStreams()">
                            <option value="">Select Grade</option>
                            <!-- Will be populated by JavaScript -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="stream">Stream (Optional):</label>
                        <select name="stream" id="stream_bulk">
                            <option value="">-- No Stream (Optional) --</option>
                            <!-- Will be populated by JavaScript -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="student_file">Upload Student List (CSV or Excel):</label>
                        <input type="file" name="student_file" id="student_file" accept=".csv,.xlsx" required>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
                        <p><strong>📋 Flexible Column Support:</strong></p>
                        <p><small><strong>Required:</strong> <code>name</code> (student name)</small></p>
                        <p><small><strong>Optional:</strong> <code>admission_number</code>, <code>grade</code>, <code>stream</code>, <code>gender</code></small></p>
                        <p><small><strong>Column Name Variations Supported:</strong></small></p>
                        <ul style="font-size: 0.85em; margin: 5px 0; padding-left: 20px;">
                            <li><strong>Name:</strong> name, student_name, full_name</li>
                            <li><strong>Admission:</strong> admission_number, addmission_number, adm_number, reg_number</li>
                            <li><strong>Grade:</strong> grade (will auto-assign to correct grade/stream)</li>
                            <li><strong>Stream:</strong> stream (will auto-assign to correct stream)</li>
                        </ul>
                        <p><small>💡 <strong>Your file format works!</strong> The system will automatically handle your column names and generate admission numbers if missing.</small></p>
                    </div>
                    <div class="template-links" style="margin-bottom: 15px;">
                        <p><strong>📥 Download Templates:</strong></p>

                        <!-- Full Templates with Sample Data -->
                        <div style="margin-bottom: 10px;">
                            <p style="font-weight: 600; margin-bottom: 5px; color: #1f7d53;">📋 Full Templates (with sample data):</p>
                            <a href="{{ url_for('classteacher.download_student_template', format='xlsx', type='full') }}" class="download-btn" style="display: inline-block; margin-right: 10px;">
                                <i class="fa fa-file-excel"></i> Excel Template (Full)
                            </a>
                            <a href="{{ url_for('classteacher.download_student_template', format='csv', type='full') }}" class="download-btn" style="display: inline-block;">
                                <i class="fa fa-file-text"></i> CSV Template (Full)
                            </a>
                        </div>

                        <!-- Minimal Templates -->
                        <div style="margin-bottom: 10px;">
                            <p style="font-weight: 600; margin-bottom: 5px; color: #6c757d;">📄 Minimal Templates (fewer examples):</p>
                            <a href="{{ url_for('classteacher.download_student_template', format='xlsx', type='minimal') }}" class="download-btn" style="display: inline-block; margin-right: 10px; background: #6c757d;">
                                <i class="fa fa-file-excel"></i> Excel (Minimal)
                            </a>
                            <a href="{{ url_for('classteacher.download_student_template', format='csv', type='minimal') }}" class="download-btn" style="display: inline-block; background: #6c757d;">
                                <i class="fa fa-file-text"></i> CSV (Minimal)
                            </a>
                        </div>

                        <!-- Headers Only -->
                        <div>
                            <p style="font-weight: 600; margin-bottom: 5px; color: #dc3545;">📝 Headers Only (blank template):</p>
                            <a href="{{ url_for('classteacher.download_student_template', format='csv', type='headers_only') }}" class="download-btn" style="display: inline-block; background: #dc3545;">
                                <i class="fa fa-file-text"></i> CSV (Headers Only)
                            </a>
                        </div>

                        <div style="margin-top: 10px; padding: 10px; background: #e7f3ff; border-radius: 5px; font-size: 0.9em;">
                            <strong>💡 Recommendation:</strong> Use the <strong>Full Excel Template</strong> for best experience - it includes instructions and formatting!
                        </div>
                    </div>
                    <button type="submit" class="manage-btn">Upload Students</button>
                </form>
            </div>
        </div>

        <!-- Display Existing Students -->
        <div class="students-section">
            <h2>Existing Students</h2>
            {% if grade and stream %}
            <p class="teacher-info">You are assigned to Grade {{ grade }} Stream {{ stream }}</p>
            {% else %}
            <p class="teacher-info">You are not assigned to any specific class. You can manage students across all grades and streams.</p>
            {% endif %}

            <!-- Filter Form -->
            <div class="filter-form">
                <h3>Filter Students</h3>
                <form method="GET" action="{{ url_for('classteacher.manage_students') }}">
                    <div class="filter-grid">
                        <div class="form-group">
                            <label for="search">Search:</label>
                            <input type="text" name="search" id="search" placeholder="Search by name or admission number" value="{{ request.args.get('search', '') }}">
                        </div>
                        <div class="form-group">
                            <label for="filter_educational_level">Educational Level:</label>
                            <select name="educational_level" id="filter_educational_level">
                                <option value="">All Levels</option>
                                {% for level in educational_levels %}
                                    <option value="{{ level }}" {% if request.args.get('educational_level') == level %}selected{% endif %}>
                                        {{ level|replace('_', ' ')|title }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="filter_grade">Grade:</label>
                            <select name="grade_id" id="filter_grade">
                                <option value="">All Grades</option>
                                {% for grade_obj in grades %}
                                    <option value="{{ grade_obj.id }}" {% if request.args.get('grade_id')|int == grade_obj.id %}selected{% endif %}>
                                        {{ grade_obj.level }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="filter_stream">Stream:</label>
                            <select name="stream_id" id="filter_stream">
                                <option value="">All Streams</option>
                                <!-- Will be populated by JavaScript -->
                            </select>
                        </div>
                        <div class="form-group filter-buttons">
                            <button type="submit" class="filter-btn">Apply Filters</button>
                            <a href="{{ url_for('classteacher.manage_students') }}" class="reset-btn">Reset Filters</a>
                        </div>
                    </div>
                </form>
            </div>

            {% if students %}
                <!-- Pagination Info -->
                <div class="pagination-info">
                    <p>Showing {{ students|length }} of {{ total_students }} students (Page {{ pagination.page }} of {{ pagination.pages }})</p>
                </div>

                <!-- Bulk Actions Section -->
                <div class="bulk-actions-section">
                    <h3>Bulk Actions</h3>
                    <div class="bulk-actions-grid">
                        <!-- Delete Buttons -->
                        <div class="bulk-action-group">
                            <h4>Delete Actions</h4>
                            <button type="button" class="delete-all-btn" onclick="deleteAllStudents()">Delete All Learners</button>
                            <button type="button" class="delete-selected-btn" onclick="deleteSelectedStudents()">Delete Selected Learners</button>
                        </div>

                        <!-- Bulk Edit Form -->
                        <div class="bulk-action-group">
                            <h4>Edit Selected Students</h4>
                            <form id="bulk-edit-form" method="POST" action="{{ url_for('classteacher.manage_students') }}">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                <input type="hidden" name="action" value="bulk_edit_students">

                                <div class="bulk-edit-options">
                                    <div class="form-group">
                                        <label for="bulk_edit_type">Edit Type:</label>
                                        <select name="bulk_edit_type" id="bulk_edit_type" onchange="toggleBulkEditFields()">
                                            <option value="">Select Action</option>
                                            <option value="gender">Update Gender</option>
                                            <option value="stream">Move to Stream</option>
                                        </select>
                                    </div>

                                    <!-- Gender Edit Options (initially hidden) -->
                                    <div id="gender_edit_options" class="edit-option-group" style="display: none;">
                                        <div class="form-group">
                                            <label for="bulk_gender">Gender:</label>
                                            <select name="bulk_gender" id="bulk_gender">
                                                <option value="">Select Gender</option>
                                                <option value="Male">Male</option>
                                                <option value="Female">Female</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Stream Edit Options (initially hidden) -->
                                    <div id="stream_edit_options" class="edit-option-group" style="display: none;">
                                        <div class="form-group">
                                            <label for="bulk_grade">Grade:</label>
                                            <select name="bulk_grade" id="bulk_grade" onchange="updateBulkEditStreams()">
                                                <option value="">Select Grade</option>
                                                {% for grade_obj in grades %}
                                                    <option value="{{ grade_obj.id }}">{{ grade_obj.level }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="bulk_stream">Stream:</label>
                                            <select name="bulk_stream" id="bulk_stream" disabled>
                                                <option value="">Select Stream</option>
                                                <!-- Will be populated by JavaScript -->
                                            </select>
                                        </div>
                                    </div>

                                    <button type="button" class="update-btn" onclick="bulkEditStudents()">Apply Changes</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Download Buttons -->
                <div class="download-buttons" style="margin-bottom: 15px;">
                    <h3>Download Class Lists</h3>
                    <div class="download-grid">
                        <a href="{{ url_for('classteacher.download_class_list', search=request.args.get('search', ''), educational_level=request.args.get('educational_level', ''), grade_id=request.args.get('grade_id', ''), stream_id=request.args.get('stream_id', '')) }}" class="download-btn">
                            <i class="fa fa-download"></i> Download Current View
                        </a>
                        <a href="{{ url_for('classteacher.download_class_list', all_grades=1) }}" class="download-btn">
                            <i class="fa fa-download"></i> Download All Students
                        </a>
                        <div class="download-select">
                            <select id="download_grade" onchange="updateDownloadStreams()">
                                <option value="">Select Grade to Download</option>
                                {% for grade_obj in grades %}
                                    <option value="{{ grade_obj.id }}">{{ grade_obj.level }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="download-select">
                            <select id="download_stream" disabled>
                                <option value="">Select Stream to Download</option>
                                <!-- Will be populated by JavaScript -->
                            </select>
                        </div>
                        <button type="button" class="download-btn" onclick="downloadSelectedClass()">Download Selected Class</button>
                    </div>
                </div>

                <div class="table-responsive">
                    <form id="bulk-delete-form" method="POST" action="{{ url_for('classteacher.manage_students') }}">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <input type="hidden" name="action" value="bulk_delete_students">
                        <form id="bulk-update-form" method="POST" action="{{ url_for('classteacher.manage_students') }}">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <input type="hidden" name="action" value="bulk_update_genders">
                            <table id="students-table">
                                <thead>
                                    <tr>
                                        <th class="checkbox-column">
                                            <input type="checkbox" id="select-all" onclick="toggleSelectAll()">
                                        </th>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Admission Number</th>
                                        <th>Grade</th>
                                        <th>Stream</th>
                                        <th>Gender</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for student in students %}
                                        <tr data-grade="{{ student.stream.grade.name if student.stream else '' }}">
                                            <td class="checkbox-column">
                                                <input type="checkbox" name="student_ids" value="{{ student.id }}" class="student-checkbox">
                                            </td>
                                            <td>{{ student.id }}</td>
                                            <td>{{ student.name }}</td>
                                            <td>{{ student.admission_number }}</td>
                                            <td>{{ student.stream.grade.name if student.stream else 'N/A' }}</td>
                                            <td>{{ student.stream.name if student.stream else 'No Stream' }}</td>
                                            <td>
                                                <select name="gender_{{ student.id }}">
                                                    <option value="" {% if not student.gender %}selected{% endif %}>Not Set</option>
                                                    <option value="Male" {% if student.gender == 'male' %}selected{% endif %}>Male</option>
                                                    <option value="Female" {% if student.gender == 'female' %}selected{% endif %}>Female</option>
                                                </select>
                                            </td>
                                            <td>
                                                <form method="POST" action="{{ url_for('classteacher.manage_students') }}" style="display:inline;">
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                                    <input type="hidden" name="action" value="delete_student">
                                                    <input type="hidden" name="student_id" value="{{ student.id }}">
                                                    <button type="submit" class="delete-btn">Delete</button>
                                                </form>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </form>
                    </form>
                </div>

                <!-- Pagination Controls -->
                <div class="pagination-controls">
                    <ul class="pagination">
                        {% if pagination.has_prev %}
                            <li><a href="{{ url_for('classteacher.manage_students', page=1, search=request.args.get('search', ''), educational_level=request.args.get('educational_level'), grade_id=request.args.get('grade_id'), stream_id=request.args.get('stream_id')) }}">&laquo; First</a></li>
                            <li><a href="{{ url_for('classteacher.manage_students', page=pagination.prev_num, search=request.args.get('search', ''), educational_level=request.args.get('educational_level'), grade_id=request.args.get('grade_id'), stream_id=request.args.get('stream_id')) }}">&lsaquo; Previous</a></li>
                        {% else %}
                            <li class="disabled"><span>&laquo; First</span></li>
                            <li class="disabled"><span>&lsaquo; Previous</span></li>
                        {% endif %}

                        {% for page_num in pagination.iter_pages(left_edge=2, left_current=2, right_current=3, right_edge=2) %}
                            {% if page_num %}
                                {% if page_num == pagination.page %}
                                    <li class="active"><span>{{ page_num }}</span></li>
                                {% else %}
                                    <li><a href="{{ url_for('classteacher.manage_students', page=page_num, search=request.args.get('search', ''), educational_level=request.args.get('educational_level'), grade_id=request.args.get('grade_id'), stream_id=request.args.get('stream_id')) }}">{{ page_num }}</a></li>
                                {% endif %}
                            {% else %}
                                <li class="disabled"><span>...</span></li>
                            {% endif %}
                        {% endfor %}

                        {% if pagination.has_next %}
                            <li><a href="{{ url_for('classteacher.manage_students', page=pagination.next_num, search=request.args.get('search', ''), educational_level=request.args.get('educational_level'), grade_id=request.args.get('grade_id'), stream_id=request.args.get('stream_id')) }}">Next &rsaquo;</a></li>
                            <li><a href="{{ url_for('classteacher.manage_students', page=pagination.pages, search=request.args.get('search', ''), educational_level=request.args.get('educational_level'), grade_id=request.args.get('grade_id'), stream_id=request.args.get('stream_id')) }}">Last &raquo;</a></li>
                        {% else %}
                            <li class="disabled"><span>Next &rsaquo;</span></li>
                            <li class="disabled"><span>Last &raquo;</span></li>
                        {% endif %}
                    </ul>
                </div>
            {% else %}
                <p>No students added yet.</p>
            {% endif %}
        </div>
    </div>

    <script>
        const allGrades = {{ grades | tojson }};
        const educationalLevelMapping = {{ educational_level_mapping | tojson }};

        document.addEventListener('DOMContentLoaded', function() {
            const successMessage = "{{ success_message|safe if success_message else '' }}";
            const errorMessage = "{{ error_message|safe if error_message else '' }}";

            if (successMessage) {
                showNotification(successMessage, 'success');
            }
            if (errorMessage) {
                showNotification(errorMessage, 'error');
            }

            // Auto-hide flash messages after 5 seconds
            setTimeout(() => {
                const flashMessages = document.querySelectorAll('.message');
                flashMessages.forEach(message => {
                    message.style.opacity = '0';
                    setTimeout(() => {
                        message.style.display = 'none';
                    }, 500);
                });
            }, 5000);

            const urlParams = new URLSearchParams(window.location.search);
            const savedLevel = urlParams.get('level') || localStorage.getItem('selectedEducationalLevel') || '';

            if (savedLevel) {
                document.getElementById('educational_level').value = savedLevel;
            }

            updateAllForms();

            // Initialize stream dropdowns
            updateStreams();
            updateBulkStreams();

            // Initialize filter form stream dropdown
            const filterGradeSelect = document.getElementById('filter_grade');
            if (filterGradeSelect) {
                filterGradeSelect.addEventListener('change', updateFilterStreams);
                // Initialize filter streams on page load
                updateFilterStreams();
            }

            // Initialize download form stream dropdown
            const downloadGradeSelect = document.getElementById('download_grade');
            if (downloadGradeSelect) {
                downloadGradeSelect.addEventListener('change', updateDownloadStreams);
            }
        });

        function showNotification(message, type) {
            const container = document.getElementById('message-container');
            const notification = document.createElement('div');
            notification.className = `message message-${type}`;
            notification.textContent = message;
            container.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        function updateAllForms() {
            const educationalLevel = document.getElementById('educational_level').value;

            localStorage.setItem('selectedEducationalLevel', educationalLevel);

            document.getElementById('educational_level_student').value = educationalLevel;
            document.getElementById('educational_level_bulk').value = educationalLevel;

            updateGradeOptions();
            updateStreams();
            updateBulkStreams();
        }

        function updateGradeOptions() {
            const educationalLevel = document.getElementById('educational_level').value;
            const gradeSelects = [
                document.getElementById('grade'),
                document.getElementById('grade_bulk')
            ];

            if (educationalLevel) {
                const allowedGrades = educationalLevelMapping[educationalLevel] || [];

                gradeSelects.forEach(gradeSelect => {
                    if (gradeSelect) {
                        gradeSelect.innerHTML = '<option value="">Select Grade</option>';

                        allGrades.forEach(grade => {
                            if (allowedGrades.includes(grade.name)) {
                                const option = document.createElement('option');
                                option.value = grade.id;
                                option.textContent = grade.name;
                                option.setAttribute('data-level', grade.name);
                                gradeSelect.appendChild(option);
                            }
                        });
                    }
                });
            } else {
                gradeSelects.forEach(gradeSelect => {
                    if (gradeSelect) {
                        gradeSelect.innerHTML = '<option value="">Select Grade</option>';
                        allGrades.forEach(grade => {
                            const option = document.createElement('option');
                            option.value = grade.id;
                            option.textContent = grade.name;
                            option.setAttribute('data-level', grade.name);
                            gradeSelect.appendChild(option);
                        });
                    }
                });
            }
        }



        function updateStreams() {
            const gradeId = document.getElementById('grade').value;
            const streamSelect = document.getElementById('stream');

            // Clear the dropdown
            streamSelect.innerHTML = '';

            // Add the "No Stream" option
            const noStreamOption = document.createElement('option');
            noStreamOption.value = "";
            noStreamOption.textContent = "-- No Stream (Optional) --";
            streamSelect.appendChild(noStreamOption);

            if (gradeId) {
                fetch(`/classteacher/get_streams/${gradeId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.streams && data.streams.length > 0) {
                            data.streams.forEach(stream => {
                                const option = document.createElement('option');
                                option.value = stream.id;
                                option.textContent = stream.name;
                                streamSelect.appendChild(option);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching streams:', error);
                    });
            }
        }

        function updateBulkStreams() {
            const gradeId = document.getElementById('grade_bulk').value;
            const streamSelect = document.getElementById('stream_bulk');

            // Clear the dropdown
            streamSelect.innerHTML = '';

            // Add the "No Stream" option
            const noStreamOption = document.createElement('option');
            noStreamOption.value = "";
            noStreamOption.textContent = "-- No Stream (Optional) --";
            streamSelect.appendChild(noStreamOption);

            if (gradeId) {
                fetch(`/classteacher/get_streams/${gradeId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.streams && data.streams.length > 0) {
                            data.streams.forEach(stream => {
                                const option = document.createElement('option');
                                option.value = stream.id;
                                option.textContent = stream.name;
                                streamSelect.appendChild(option);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching streams:', error);
                    });
            }
        }

        function updateFilterStreams() {
            const gradeId = document.getElementById('filter_grade').value;
            const streamSelect = document.getElementById('filter_stream');
            const currentStreamId = "{{ request.args.get('stream_id', '') }}";

            // Clear the dropdown
            streamSelect.innerHTML = '';

            // Add the "All Streams" option
            const allStreamsOption = document.createElement('option');
            allStreamsOption.value = "";
            allStreamsOption.textContent = "All Streams";
            streamSelect.appendChild(allStreamsOption);

            if (gradeId) {
                fetch(`/classteacher/get_streams/${gradeId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.streams && data.streams.length > 0) {
                            data.streams.forEach(stream => {
                                const option = document.createElement('option');
                                option.value = stream.id;
                                option.textContent = stream.name;
                                if (stream.id.toString() === currentStreamId) {
                                    option.selected = true;
                                }
                                streamSelect.appendChild(option);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching streams:', error);
                    });
            }
        }

        // We're now using server-side filtering instead of client-side filtering

        function updateDownloadStreams() {
            const gradeId = document.getElementById('download_grade').value;
            const streamSelect = document.getElementById('download_stream');

            // Clear the dropdown
            streamSelect.innerHTML = '';

            // Add the "All Streams" option
            const allStreamsOption = document.createElement('option');
            allStreamsOption.value = "";
            allStreamsOption.textContent = "All Streams in Grade";
            streamSelect.appendChild(allStreamsOption);

            if (gradeId) {
                streamSelect.disabled = false;

                fetch(`/classteacher/get_streams/${gradeId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.streams && data.streams.length > 0) {
                            data.streams.forEach(stream => {
                                const option = document.createElement('option');
                                option.value = stream.id;
                                option.textContent = stream.name;
                                streamSelect.appendChild(option);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching streams:', error);
                    });
            } else {
                streamSelect.disabled = true;
            }
        }

        function updateBulkEditStreams() {
            const gradeId = document.getElementById('bulk_grade').value;
            const streamSelect = document.getElementById('bulk_stream');

            // Clear the dropdown
            streamSelect.innerHTML = '';

            // Add the default option
            const defaultOption = document.createElement('option');
            defaultOption.value = "";
            defaultOption.textContent = "Select Stream";
            streamSelect.appendChild(defaultOption);

            if (gradeId) {
                streamSelect.disabled = false;

                fetch(`/classteacher/get_streams/${gradeId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.streams && data.streams.length > 0) {
                            data.streams.forEach(stream => {
                                const option = document.createElement('option');
                                option.value = stream.id;
                                option.textContent = stream.name;
                                streamSelect.appendChild(option);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching streams:', error);
                    });
            } else {
                streamSelect.disabled = true;
            }
        }

        function toggleBulkEditFields() {
            const editType = document.getElementById('bulk_edit_type').value;
            const genderOptions = document.getElementById('gender_edit_options');
            const streamOptions = document.getElementById('stream_edit_options');

            // Hide all option groups first
            genderOptions.style.display = 'none';
            streamOptions.style.display = 'none';

            // Show the selected option group
            if (editType === 'gender') {
                genderOptions.style.display = 'block';
            } else if (editType === 'stream') {
                streamOptions.style.display = 'block';
            }
        }

        function downloadSelectedClass() {
            const gradeId = document.getElementById('download_grade').value;
            const streamId = document.getElementById('download_stream').value;

            if (!gradeId) {
                alert('Please select a grade to download.');
                return;
            }

            let url = `{{ url_for('classteacher.download_class_list') }}?grade_id=${gradeId}`;
            if (streamId) {
                url += `&stream_id=${streamId}`;
            }

            window.location.href = url;
        }

        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('select-all');
            const studentCheckboxes = document.querySelectorAll('.student-checkbox');
            studentCheckboxes.forEach(checkbox => {
                if (checkbox.closest('tr').style.display !== 'none') {
                    checkbox.checked = selectAllCheckbox.checked;
                }
            });
        }

        function deleteSelectedStudents() {
            const studentCheckboxes = document.querySelectorAll('.student-checkbox:checked');
            if (studentCheckboxes.length === 0) {
                alert('Please select at least one student to delete.');
                return;
            }

            if (confirm(`Are you sure you want to delete ${studentCheckboxes.length} selected student(s)?`)) {
                document.getElementById('bulk-delete-form').submit();
            }
        }

        function bulkEditStudents() {
            const studentCheckboxes = document.querySelectorAll('.student-checkbox:checked');
            if (studentCheckboxes.length === 0) {
                alert('Please select at least one student to edit.');
                return;
            }

            const editType = document.getElementById('bulk_edit_type').value;
            if (!editType) {
                alert('Please select an edit type.');
                return;
            }

            // Validate based on edit type
            if (editType === 'gender') {
                const gender = document.getElementById('bulk_gender').value;
                if (!gender) {
                    alert('Please select a gender.');
                    return;
                }
            } else if (editType === 'stream') {
                const gradeId = document.getElementById('bulk_grade').value;
                const streamId = document.getElementById('bulk_stream').value;

                if (!gradeId) {
                    alert('Please select a grade.');
                    return;
                }

                if (!streamId) {
                    alert('Please select a stream.');
                    return;
                }
            }

            // Create a hidden form to submit the selected student IDs
            const form = document.getElementById('bulk-edit-form');

            // Remove any existing student ID inputs
            document.querySelectorAll('input[name="student_ids"]').forEach(input => {
                input.remove();
            });

            // Add the selected student IDs to the form
            studentCheckboxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'student_ids';
                input.value = checkbox.value;
                form.appendChild(input);
            });

            // Confirm and submit
            const actionText = editType === 'gender' ? 'update the gender of' : 'move';
            if (confirm(`Are you sure you want to ${actionText} ${studentCheckboxes.length} selected student(s)?`)) {
                form.submit();
            }
        }

        function deleteAllStudents() {
            const visibleRows = Array.from(document.querySelectorAll('#students-table tbody tr'))
                .filter(row => row.style.display !== 'none');

            if (visibleRows.length === 0) {
                alert('No students to delete.');
                return;
            }

            if (confirm(`Are you sure you want to delete all ${visibleRows.length} visible student(s)?`)) {
                visibleRows.forEach(row => {
                    const checkbox = row.querySelector('.student-checkbox');
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
                document.getElementById('bulk-delete-form').submit();
            }
        }

        document.querySelectorAll('.delete-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('Are you sure you want to delete this student?')) {
                    e.preventDefault();
                }
            });
        });

        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function() {
                const educationalLevel = document.getElementById('educational_level').value;

                if (educationalLevel) {
                    const hiddenField = document.createElement('input');
                    hiddenField.type = 'hidden';
                    hiddenField.name = 'redirect_level';
                    hiddenField.value = educationalLevel;
                    form.appendChild(hiddenField);
                }
            });
        });
    </script>
</body>
</html>