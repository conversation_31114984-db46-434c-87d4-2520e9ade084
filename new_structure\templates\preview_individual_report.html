<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="robots" content="noindex" />
    <title>Academic Report</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <style>
      body {
        font-family: "Poppins", sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f4f4f4;
      }
      .report-container {
        max-width: 800px;
        margin: 0 auto;
        background-color: white;
        padding: 20px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        margin-bottom: 20px;
      }
      .header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
      }
      .header p {
        margin: 5px 0;
        font-size: 16px;
        color: #555;
      }
      .student-details p {
        margin: 5px 0;
        font-size: 14px;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }
      th,
      td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: center;
      }
      th {
        background-color: #4caf50;
        color: white;
        font-weight: bold;
      }
      tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      .remarks {
        margin-top: 20px;
      }
      .remarks h3 {
        font-size: 16px;
        color: #333;
      }
      .remarks p {
        margin: 5px 0;
        font-size: 14px;
      }
      .footer {
        text-align: center;
        margin-top: 20px;
        font-size: 12px;
        color: #777;
      }
      .back-btn {
        display: inline-block;
        margin-bottom: 20px;
        padding: 10px 20px;
        background-color: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 5px;
      }
      .back-btn:hover {
        background-color: #0056b3;
      }

      /* Component row styling */
      .component-row {
        background-color: #f8f9fa !important;
        font-size: 0.9em;
        border-left: 3px solid #4caf50;
      }

      .component-name {
        text-align: left !important;
        padding-left: 30px !important;
        font-style: italic;
        color: #555;
      }

      .component-mark {
        color: #666;
        font-weight: normal;
      }

      .component-label {
        font-style: italic;
        color: #666;
        font-size: 0.85em;
      }

      /* Print styles */
      @media print {
        /* Remove all browser-generated content */
        @page {
          size: portrait;
          margin: 1cm;
          /* Remove headers and footers completely */
          @top-left {
            content: "";
          }
          @top-center {
            content: "";
          }
          @top-right {
            content: "";
          }
          @bottom-left {
            content: "";
          }
          @bottom-center {
            content: "";
          }
          @bottom-right {
            content: "";
          }
        }

        /* Reset all styles for clean printing */
        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        html {
          margin: 0 !important;
          padding: 0 !important;
        }

        body {
          background-color: white !important;
          padding: 0 !important;
          margin: 0 !important;
          font-size: 12pt !important;
          font-family: Arial, sans-serif !important;
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
        }

        .report-container {
          box-shadow: none !important;
          padding: 0 !important;
          max-width: 100% !important;
          margin: 0 !important;
          background-color: white !important;
        }

        .back-btn,
        .no-print,
        button {
          display: none !important;
          visibility: hidden !important;
        }

        .logo-container img {
          max-width: 120px !important;
          height: auto !important;
        }

        /* Ensure colors print correctly */
        th {
          background-color: #4caf50 !important;
          color: white !important;
          -webkit-print-color-adjust: exact !important;
        }

        .component-row {
          background-color: #f8f9fa !important;
          -webkit-print-color-adjust: exact !important;
        }

        table {
          page-break-inside: auto !important;
        }

        tr {
          page-break-inside: avoid !important;
          page-break-after: auto !important;
        }

        thead {
          display: table-header-group !important;
        }
      }
    </style>
  </head>
  <body>
    <script>
      // This script helps ensure proper printing
      function handlePrint() {
        // Simply use the browser's built-in print functionality with clean CSS
        window.print();
        return false;
      }

      // For auto-print when print=1 is in the URL
      {% if print_mode %}
      window.onload = function() {
        handlePrint();
      };
      {% endif %}
    </script>
    <div class="report-container">
      <div
        style="display: flex; gap: 10px; margin-bottom: 20px"
        {%
        if
        print_mode
        %}class="no-print"
        {%
        endif
        %}
      >
        <a href="{{ url_for('classteacher.dashboard') }}" class="back-btn"
          >Back to Dashboard</a
        >
        <button
          onclick="handlePrint()"
          style="
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
          "
        >
          <span style="font-size: 16px">🖨️</span> Print Report
        </button>
      </div>
      <div class="header">
        <div class="logo-container">
          <img
            src="{{ url_for('static', filename='images/kirima_logo.png') }}"
            alt="School Logo"
            style="max-width: 150px; height: auto; margin-bottom: 10px"
          />
        </div>
        <h1>KIRIMA PRIMARY SCHOOL</h1>
        <p>P.O. BOX 123, KIRIMA | TEL: +254 123 456789</p>
        <p>
          Email: <EMAIL> | Website: www.kirimaprimary.ac.ke
        </p>
        <p>
          ACADEMIC REPORT TERM {{ term.replace('_', ' ').upper() }} {{
          academic_year|default('2023') }}
        </p>
      </div>

      <div class="student-details">
        <p>{{ student_data.student.upper() }} ADM NO.: {{ admission_no }}</p>
        <p>Grade {{ grade }} {{ education_level }} {{ stream }}</p>
        <p>
          Mean Points: {{ mean_points }} Total Marks: {{ total | int }} out of:
          {{ total_possible_marks }}
        </p>
        <p>Mean Mark: {{ avg_percentage | round(2) }}%</p>
        <p>Total Points: {{ total_points }}</p>
      </div>

      <table>
        <thead>
          <tr>
            <th>Subjects</th>
            {% if assessment_type.lower() == 'end_term' or
            assessment_type.lower() == 'endterm' %}
            <!-- Show all assessment columns for end term comparison -->
            <th>Entrance</th>
            <th>Mid Term</th>
            <th>End Term</th>
            <th>Avg.</th>
            {% else %}
            <!-- Show only current assessment column for single assessments -->
            <th>{{ assessment_type.replace('_', ' ').title() }}</th>
            {% endif %}
            <th>Subject Remarks</th>
          </tr>
        </thead>
        <tbody>
          {% for row in table_data %}
          <tr>
            <td style="text-align: left; font-weight: bold">
              {{ row.subject }}
            </td>
            {% if assessment_type.lower() == 'end_term' or
            assessment_type.lower() == 'endterm' %}
            <!-- Show all assessment columns for end term comparison -->
            <td>{{ row.entrance | int if row.entrance != 0 else '-' }}</td>
            <td>{{ row.mid_term | int if row.mid_term != 0 else '-' }}</td>
            <td>{{ row.end_term | int if row.end_term != 0 else '-' }}</td>
            <td>{{ row.avg | int if row.avg != 0 else '-' }}</td>
            {% else %}
            <!-- Show only current assessment mark for single assessments -->
            <td>
              {{ row.current_assessment | int if row.current_assessment != 0
              else '-' }}
            </td>
            {% endif %}
            <td>{{ row.remarks | replace(' (TBD)', '') }}</td>
          </tr>

          <!-- Show component breakdown for composite subjects -->
          {% if composite_data and composite_data.get(row.subject) %} {% set
          components = composite_data[row.subject].components %} {% for
          component_name, component_data in components.items() %}
          <tr class="component-row">
            <td class="component-name">
              {{ component_name }}
              <span style="font-size: 0.8em; color: #888; font-style: italic">
                ({{ component_data.mark }}/{{ component_data.max_mark }})
              </span>
            </td>
            {% if assessment_type.lower() == 'end_term' or
            assessment_type.lower() == 'endterm' %}
            <!-- Show component marks for all assessment columns -->
            <td class="component-mark">
              {{ component_data.mark if component_data.mark != 0 else '-' }}
            </td>
            <td class="component-mark">
              {{ component_data.mark if component_data.mark != 0 else '-' }}
            </td>
            <td class="component-mark">
              {{ component_data.mark if component_data.mark != 0 else '-' }}
            </td>
            <td class="component-mark">
              {{ component_data.mark if component_data.mark != 0 else '-' }}
            </td>
            {% else %}
            <!-- Show component mark for current assessment only -->
            <td class="component-mark">
              {{ component_data.mark if component_data.mark != 0 else '-' }}
            </td>
            {% endif %}
            <td class="component-label">
              {{ component_data.remarks | replace(' (TBD)', '') }}
            </td>
          </tr>
          {% endfor %} {% endif %} {% endfor %}
          <tr>
            <td>Totals</td>
            {% if assessment_type.lower() == 'end_term' or
            assessment_type.lower() == 'endterm' %}
            <!-- Show totals for all assessment columns -->
            <td></td>
            <td></td>
            <td>{{ total | int }}</td>
            <td>{{ total | int }}</td>
            {% else %}
            <!-- Show total for current assessment only -->
            <td>{{ total | int }}</td>
            {% endif %}
            <td></td>
          </tr>
        </tbody>
      </table>

      <div class="remarks">
        <h3>Class Teacher's Remarks:</h3>
        <p>
          Well done! With continued focus and consistency, you have the
          potential to achieve even more.
        </p>
        <p>Class Teacher: Moses Barasa</p>
        <h3>Head Teacher's Remarks:</h3>
        <p>
          Great progress! Your growing confidence is evident - keep practicing,
          and you'll excel even further.
        </p>
        <p>Head Teacher Name: Mr. Paul Mwangi</p>
        <p>Head Teacher Signature: ____________________</p>
        <p>Next Term Begins on: TBD</p>
      </div>

      <div class="footer">
        <p>Generated on: {{ current_date }}</p>
        <p>Kirima School powered by CbcTeachkit</p>
      </div>
    </div>
  </body>
</html>
